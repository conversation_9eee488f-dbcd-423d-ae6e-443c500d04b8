import discord
from discord.ext import commands
import sqlite3
import random
from datetime import datetime, timedelta
from contextlib import contextmanager
import json
import time

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

class BankSystem:
    def __init__(self):
        # Database connection parameters
        self.max_retries = 5
        self.retry_delay = 0.1
        
        # Currency settings
        self.currency_name = "Coins"
        self.currency_emoji = "🪙"
        
        # Work settings
        self.work_cooldown_hours = 1  # 1 hour cooldown for work
        self.collect_cooldown_hours = 24  # 24 hour cooldown for collect
        self.work_min_amount = 10
        self.work_max_amount = 50
        self.collect_amount = 100
        
        # Initialize database
        self.init_database()
        
        # Work quotes
        self.work_quotes = [
            "You worked at the local shop and earned",
            "You completed a delivery job and received",
            "You helped a neighbor and they gave you",
            "You did some freelance work and made",
            "You sold some items and earned",
            "You worked overtime and got paid",
            "You completed a task and received",
            "You did some odd jobs and made"
        ]

    def init_database(self):
        """Initialize the SQLite database with required tables"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                c.execute('BEGIN TRANSACTION')
                
                # Create users table
                c.execute('''CREATE TABLE IF NOT EXISTS users
                            (user_id TEXT PRIMARY KEY,
                             username TEXT,
                             balance INTEGER DEFAULT 0,
                             created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')
                
                # Create cooldowns table
                c.execute('''CREATE TABLE IF NOT EXISTS cooldowns
                            (user_id TEXT PRIMARY KEY,
                             work_cooldown TEXT,
                             collect_cooldown TEXT)''')
                
                # Create transactions table for logging
                c.execute('''CREATE TABLE IF NOT EXISTS transactions
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT,
                             amount INTEGER,
                             type TEXT,
                             timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                             details TEXT)''')
                
                # Create indexes for performance
                c.execute('CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)')
                c.execute('CREATE INDEX IF NOT EXISTS idx_cooldowns_user_id ON cooldowns(user_id)')
                c.execute('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)')
                
                conn.commit()
                print("Database initialization complete!")
                
            except Exception as e:
                conn.rollback()
                print(f"Error initializing database: {e}")
                raise

    @contextmanager
    def get_db_connection(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = sqlite3.connect('bank.db', timeout=60.0)
            conn.row_factory = sqlite3.Row
            conn.execute('PRAGMA journal_mode=WAL')
            conn.execute('PRAGMA busy_timeout=30000')
            conn.execute('PRAGMA synchronous=NORMAL')
            yield conn
            conn.commit()
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise e
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def get_user_balance(self, user_id: str) -> int:
        """Get user's current balance"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT balance FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            return result[0] if result else 0

    def update_balance(self, user_id: str, amount: int, username: str = None):
        """Update user balance and create user if doesn't exist"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                c.execute('BEGIN TRANSACTION')
                
                # Check if user exists
                c.execute('SELECT balance FROM users WHERE user_id = ?', (user_id,))
                result = c.fetchone()
                
                if result:
                    # Update existing user
                    new_balance = max(0, result[0] + amount)  # Prevent negative balance
                    c.execute('UPDATE users SET balance = ? WHERE user_id = ?', 
                             (new_balance, user_id))
                else:
                    # Create new user
                    new_balance = max(0, amount)
                    c.execute('''INSERT INTO users (user_id, username, balance)
                                VALUES (?, ?, ?)''', (user_id, username, new_balance))
                
                conn.commit()
                return new_balance
                
            except Exception as e:
                conn.rollback()
                print(f"Error updating balance: {e}")
                raise

    def get_cooldown(self, user_id: str, cooldown_type: str) -> str:
        """Get cooldown time for user"""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                c.execute(f'SELECT {cooldown_type} FROM cooldowns WHERE user_id = ?', (user_id,))
                result = c.fetchone()
                return result[0] if result else None
        except Exception as e:
            print(f"Error getting cooldown: {e}")
            return None

    def set_cooldown(self, user_id: str, cooldown_type: str, time_str: str):
        """Set cooldown for user"""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                c.execute('''INSERT INTO cooldowns (user_id, ''' + cooldown_type + ''')
                           VALUES (?, ?)
                           ON CONFLICT(user_id) 
                           DO UPDATE SET ''' + cooldown_type + ''' = ?''',
                        (user_id, time_str, time_str))
                conn.commit()
        except Exception as e:
            print(f"Error setting cooldown: {e}")
            raise

    def log_transaction(self, user_id: str, amount: int, transaction_type: str, details: str):
        """Log a transaction"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                c.execute('BEGIN TRANSACTION')
                c.execute('''INSERT INTO transactions 
                            (user_id, amount, type, timestamp, details)
                            VALUES (?, ?, ?, datetime('now'), ?)''',
                         (user_id, amount, transaction_type, details))
                conn.commit()
            except Exception as e:
                conn.rollback()
                print(f"Error logging transaction: {e}")
                raise

    def is_on_cooldown(self, user_id: str, cooldown_type: str, hours: int) -> tuple:
        """Check if user is on cooldown. Returns (is_on_cooldown, remaining_time)"""
        cooldown_time = self.get_cooldown(user_id, cooldown_type)
        if not cooldown_time:
            return False, 0
        
        try:
            last_time = datetime.fromisoformat(cooldown_time)
            now = datetime.now()
            time_diff = now - last_time
            cooldown_duration = timedelta(hours=hours)
            
            if time_diff < cooldown_duration:
                remaining = cooldown_duration - time_diff
                return True, remaining.total_seconds()
            else:
                return False, 0
        except:
            return False, 0

    def format_time(self, seconds: float) -> str:
        """Format seconds into readable time string"""
        if seconds < 60:
            return f"{int(seconds)} seconds"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            return f"{minutes} minutes"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            if minutes > 0:
                return f"{hours} hours and {minutes} minutes"
            else:
                return f"{hours} hours"

# Initialize bank system
bank = BankSystem()

@bot.event
async def on_ready():
    print(f'{bot.user} has connected to Discord!')
    print(f'Bot is ready and connected to {len(bot.guilds)} guilds')

@bot.command(name='work')
async def work_command(ctx):
    """Work to earn money"""
    user_id = str(ctx.author.id)
    username = str(ctx.author)
    
    # Check cooldown
    is_cooldown, remaining_time = bank.is_on_cooldown(user_id, 'work_cooldown', bank.work_cooldown_hours)
    
    if is_cooldown:
        time_left = bank.format_time(remaining_time)
        embed = discord.Embed(
            title="⏰ Work Cooldown",
            description=f"You need to wait **{time_left}** before you can work again!",
            color=discord.Color.red()
        )
        await ctx.send(embed=embed)
        return
    
    # Generate random work amount
    work_amount = random.randint(bank.work_min_amount, bank.work_max_amount)
    
    # Update balance
    new_balance = bank.update_balance(user_id, work_amount, username)
    
    # Set cooldown
    bank.set_cooldown(user_id, 'work_cooldown', datetime.now().isoformat())
    
    # Log transaction
    work_quote = random.choice(bank.work_quotes)
    bank.log_transaction(user_id, work_amount, 'work', f"Work command: {work_quote}")
    
    # Send response
    embed = discord.Embed(
        title="💼 Work Complete!",
        description=f"{work_quote} **{work_amount}** {bank.currency_emoji} {bank.currency_name}!",
        color=discord.Color.green()
    )
    embed.add_field(name="💰 New Balance", value=f"{new_balance} {bank.currency_emoji}", inline=False)
    embed.set_footer(text=f"Next work available in {bank.work_cooldown_hours} hour(s)")
    
    await ctx.send(embed=embed)

@bot.command(name='collect')
async def collect_command(ctx):
    """Collect daily bonus"""
    user_id = str(ctx.author.id)
    username = str(ctx.author)
    
    # Check cooldown
    is_cooldown, remaining_time = bank.is_on_cooldown(user_id, 'collect_cooldown', bank.collect_cooldown_hours)
    
    if is_cooldown:
        time_left = bank.format_time(remaining_time)
        embed = discord.Embed(
            title="⏰ Daily Bonus Cooldown",
            description=f"You need to wait **{time_left}** before you can collect your daily bonus again!",
            color=discord.Color.red()
        )
        await ctx.send(embed=embed)
        return
    
    # Update balance
    new_balance = bank.update_balance(user_id, bank.collect_amount, username)
    
    # Set cooldown
    bank.set_cooldown(user_id, 'collect_cooldown', datetime.now().isoformat())
    
    # Log transaction
    bank.log_transaction(user_id, bank.collect_amount, 'collect', "Daily bonus collection")
    
    # Send response
    embed = discord.Embed(
        title="🎁 Daily Bonus Collected!",
        description=f"You collected your daily bonus of **{bank.collect_amount}** {bank.currency_emoji} {bank.currency_name}!",
        color=discord.Color.gold()
    )
    embed.add_field(name="💰 New Balance", value=f"{new_balance} {bank.currency_emoji}", inline=False)
    embed.set_footer(text="Come back tomorrow for another bonus!")
    
    await ctx.send(embed=embed)

@bot.command(name='balance')
async def balance_command(ctx, member: discord.Member = None):
    """Check balance (your own or someone else's)"""
    target_user = member if member else ctx.author
    user_id = str(target_user.id)
    
    balance = bank.get_user_balance(user_id)
    
    if target_user == ctx.author:
        title = "💰 Your Balance"
        description = f"You have **{balance}** {bank.currency_emoji} {bank.currency_name}"
    else:
        title = f"💰 {target_user.display_name}'s Balance"
        description = f"{target_user.display_name} has **{balance}** {bank.currency_emoji} {bank.currency_name}"
    
    embed = discord.Embed(
        title=title,
        description=description,
        color=discord.Color.blue()
    )
    embed.set_thumbnail(url=target_user.avatar.url if target_user.avatar else target_user.default_avatar.url)
    
    await ctx.send(embed=embed)

# Run the bot
if __name__ == "__main__":
    try:
        # Try to import config file
        from config import BOT_TOKEN
        bot.run(BOT_TOKEN)
    except ImportError:
        print("Error: config.py file not found!")
        print("Please copy config_example.py to config.py and add your bot token.")
    except Exception as e:
        print(f"Error starting bot: {e}")
