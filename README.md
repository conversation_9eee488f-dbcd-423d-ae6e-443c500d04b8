# Discord Bank Bot

A simple Discord bot that implements a basic banking system with work, collect, and balance commands.

## Features

- **Work Command** (`!work`): Earn coins by working (1-hour cooldown)
- **Collect Command** (`!collect`): Collect daily bonus (24-hour cooldown)  
- **Balance Command** (`!balance`): Check your balance or someone else's balance

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Create a Discord Bot**:
   - Go to https://discord.com/developers/applications
   - Create a new application
   - Go to the "Bot" section
   - Create a bot and copy the token

3. **Configure the Bot**:
   - Open `bank_bot.py`
   - Replace `'YOUR_BOT_TOKEN'` with your actual bot token

4. **Run the Bot**:
   ```bash
   python bank_bot.py
   ```

## Commands

- `!work` - Work to earn 10-50 coins (1-hour cooldown)
- `!collect` - Collect daily bonus of 100 coins (24-hour cooldown)
- `!balance` - Check your balance
- `!balance @user` - Check another user's balance

## Database

The bot uses SQLite database (`bank.db`) to store:
- User balances
- Cooldown timers
- Transaction history

The database is automatically created when you first run the bot.

## Customization

You can customize the bot by modifying these values in the `BankSystem` class:

- `work_cooldown_hours`: Hours between work commands (default: 1)
- `collect_cooldown_hours`: Hours between collect commands (default: 24)
- `work_min_amount`: Minimum coins from work (default: 10)
- `work_max_amount`: Maximum coins from work (default: 50)
- `collect_amount`: Daily bonus amount (default: 100)
- `currency_name`: Name of the currency (default: "Coins")
- `currency_emoji`: Emoji for the currency (default: "🪙")

## Bot Permissions

The bot needs the following permissions:
- Send Messages
- Use Slash Commands (if you want to add slash commands later)
- Read Message History
- Embed Links

## Notes

- The bot uses the same database handling method as the reference `knowladge.py` file
- All transactions are logged in the database
- Balances cannot go below 0
- The bot automatically creates user accounts when they first use a command
